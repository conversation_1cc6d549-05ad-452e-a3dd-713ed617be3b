{ "pid": 11572, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175070822, "dur": 34730, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175105554, "dur": 2070161, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175105571, "dur": 54, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175105630, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175105633, "dur": 29564, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175135208, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175135213, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175135317, "dur": 7, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175135327, "dur": 3518, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175138853, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175138858, "dur": 215, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175139080, "dur": 3, "ph": "X", "name": "ProcessMessages 455", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175139086, "dur": 74, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175139168, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175139174, "dur": 6411, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175145604, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175145613, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175145729, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041175145739, "dur": 2012235, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177157991, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177157999, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177158075, "dur": 46, "ph": "X", "name": "ProcessMessages 6648", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177158123, "dur": 1194, "ph": "X", "name": "ReadAsync 6648", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177159327, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177159333, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177159435, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177159444, "dur": 1570, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177161020, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177161023, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177161090, "dur": 3, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 11572, "tid": 21474836480, "ts": 1752041177161096, "dur": 14613, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177189312, "dur": 1566, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 11572, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 11572, "tid": 17179869184, "ts": 1752041175070742, "dur": 11, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 11572, "tid": 17179869184, "ts": 1752041175070753, "dur": 34793, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 17179869184, "ts": 1752041175105548, "dur": 70, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177190886, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 11572, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 11572, "tid": 1, "ts": 1752041173430356, "dur": 9261, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11572, "tid": 1, "ts": 1752041173439622, "dur": 52214, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 11572, "tid": 1, "ts": 1752041173491850, "dur": 7467, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177190897, "dur": 100, "ph": "X", "name": "", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041173426977, "dur": 4764, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041173431744, "dur": 1269317, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041173433449, "dur": 4595, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041173438058, "dur": 3154, "ph": "X", "name": "ProcessMessages 551", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041173441218, "dur": 1851, "ph": "X", "name": "ReadAsync 551", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041173443080, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041173443085, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041173443173, "dur": 1473, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041173444651, "dur": 1237271, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041174681936, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041174681943, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041174682018, "dur": 4344, "ph": "X", "name": "ProcessMessages 12173", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041174686371, "dur": 91, "ph": "X", "name": "ReadAsync 12173", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041174686471, "dur": 724, "ph": "X", "name": "ProcessMessages 25", "args": {} },
{ "pid": 11572, "tid": 12884901888, "ts": 1752041174687203, "dur": 13777, "ph": "X", "name": "ReadAsync 25", "args": {} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177190999, "dur": 33, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 11572, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 11572, "tid": 8589934592, "ts": 1752041173422018, "dur": 77343, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 11572, "tid": 8589934592, "ts": 1752041173499364, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 11572, "tid": 8589934592, "ts": 1752041173499373, "dur": 1708, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177191035, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 11572, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 11572, "tid": 4294967296, "ts": 1752041173358206, "dur": 1345645, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 11572, "tid": 4294967296, "ts": 1752041173380656, "dur": 15937, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 11572, "tid": 4294967296, "ts": 1752041174704697, "dur": 359798, "ph": "X", "name": "await ExecuteBuildProgram", "args": {} },
{ "pid": 11572, "tid": 4294967296, "ts": 1752041175065027, "dur": 2110731, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 11572, "tid": 4294967296, "ts": 1752041175065301, "dur": 5372, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 11572, "tid": 4294967296, "ts": 1752041177175778, "dur": 6944, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 11572, "tid": 4294967296, "ts": 1752041177180038, "dur": 53, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 11572, "tid": 4294967296, "ts": 1752041177182729, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177191045, "dur": 77, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1752041175106498, "dur":72, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041175106621, "dur":31556, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041175138189, "dur":428, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041175138653, "dur":1195, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041175139890, "dur":94, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041175139985, "dur":2020484, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041177160471, "dur":1080, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041177161583, "dur":55, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041177161720, "dur":71, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041177162050, "dur":9668, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1752041175139873, "dur":138, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752041175140038, "dur":2020427, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752041175139881, "dur":141, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752041175140023, "dur":1971, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752041175144085, "dur":454, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1752041175141995, "dur":2547, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752041175144543, "dur":2015991, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752041175139843, "dur":152, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752041175140294, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp-firstpass.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175140505, "dur":438, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175140966, "dur":542, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Cinemachine.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175141531, "dur":518, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175142131, "dur":790, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175142963, "dur":132, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.PerformanceTesting.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175143114, "dur":239, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Postprocessing.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175143378, "dur":380, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175143788, "dur":984, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175144794, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175144912, "dur":94, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175145026, "dur":298, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175145346, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175145451, "dur":216, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Splines.dll" }}
,{ "pid":12345, "tid":3, "ts":1752041175140009, "dur":6109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":3, "ts":1752041175147065, "dur":2011990, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":4, "ts":1752041175140593, "dur":2019905, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752041175140024, "dur":4566, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752041175144596, "dur":2015944, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752041175139965, "dur":74, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752041175140040, "dur":2020423, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752041175140155, "dur":2020305, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752041175140258, "dur":2020294, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752041175140377, "dur":2020113, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752041175140498, "dur":2019978, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752041175140706, "dur":2019856, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752041175140820, "dur":2019727, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752041175140760, "dur":2019765, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752041175140904, "dur":2019617, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752041175140978, "dur":2019493, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752041175141064, "dur":2019511, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752041175141217, "dur":2019353, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752041175141324, "dur":2019205, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752041175141432, "dur":2019054, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752041175141569, "dur":2018914, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752041175141630, "dur":2018901, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752041175141712, "dur":2018802, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752041175141784, "dur":2018696, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752041175141873, "dur":2018671, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041177175798, "dur":428, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1752041174840286, "dur": 193384, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752041174842018, "dur": 73932, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752041175001237, "dur": 7382, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752041175008623, "dur": 25038, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752041175010567, "dur": 21864, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752041175044380, "dur": 1986, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1752041175043743, "dur": 3005, "ph": "X", "name": "Write chrome-trace events", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1752041173428368, "dur":62, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041173428486, "dur":4022, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041173432535, "dur":834, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041173433449, "dur":2138, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041173435851, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ScriptAssembliesAndTypeDB" }}
,{ "pid":12345, "tid":0, "ts":1752041173435965, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":0, "ts":1752041173435831, "dur":189, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041173436020, "dur":70654, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041173506682, "dur":1180042, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041174687032, "dur":9009, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1752041173434472, "dur":1559, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752041173436137, "dur":1271, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\netcorerun\\netcorerun.exe" }}
,{ "pid":12345, "tid":1, "ts":1752041173437513, "dur":942, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Tools\\BuildPlayerDataGenerator\\BuildPlayerDataGenerator.exe" }}
,{ "pid":12345, "tid":1, "ts":1752041173438485, "dur":748, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173439255, "dur":1669, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173440957, "dur":111, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173441089, "dur":124, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.InputSystem.TestFramework.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173441233, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173441342, "dur":502, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173441863, "dur":214, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173442099, "dur":628, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173442748, "dur":420, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173443188, "dur":107, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173443315, "dur":214, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\UnityEngine.TestRunner.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173443548, "dur":304, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1752041173436044, "dur":7885, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1752041174683081, "dur":62, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1752041173444602, "dur":1241209, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1752041173434598, "dur":1474, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1752041173436074, "dur":70594, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752041173434587, "dur":1466, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752041173436055, "dur":66342, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1752041173502399, "dur":4173, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752041173435276, "dur":926, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1752041173436203, "dur":70488, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752041173434758, "dur":1363, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1752041173436121, "dur":70559, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752041173434838, "dur":1298, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1752041173436137, "dur":70534, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752041173435091, "dur":1077, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1752041173436169, "dur":70490, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752041173434887, "dur":1261, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1752041173436148, "dur":70518, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752041173434967, "dur":1190, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1752041173436158, "dur":70574, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752041173435116, "dur":1062, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1752041173436179, "dur":70541, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752041173435178, "dur":1008, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1752041173436186, "dur":70535, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752041173434657, "dur":1430, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1752041173436088, "dur":70575, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752041173435957, "dur":363, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1752041173436321, "dur":70376, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1752041173436092, "dur":70597, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752041173435481, "dur":755, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1752041173436238, "dur":70490, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752041173435552, "dur":701, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1752041173436254, "dur":70463, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752041173435684, "dur":587, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1752041173436272, "dur":70430, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752041173435778, "dur":512, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1752041173436291, "dur":70443, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752041173435903, "dur":406, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1752041173436310, "dur":70367, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752041173435315, "dur":899, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1752041173436215, "dur":70490, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752041173435399, "dur":825, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1752041173436224, "dur":70486, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1752041173436130, "dur":70595, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1752041173436232, "dur":70476, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1752041173436336, "dur":70351, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1752041174700926, "dur":736, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 11572, "tid": 3401, "ts": 1752041177191423, "dur": 20000, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177214311, "dur": 53, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177214576, "dur": 31, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177211506, "dur": 2799, "ph": "X", "name": "backend2.traceevents", "args": {} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177214408, "dur": 167, "ph": "X", "name": "buildprogram0.traceevents", "args": {} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177214641, "dur": 281, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 11572, "tid": 3401, "ts": 1752041177189179, "dur": 27107, "ph": "X", "name": "Write chrome-trace events", "args": {} },
