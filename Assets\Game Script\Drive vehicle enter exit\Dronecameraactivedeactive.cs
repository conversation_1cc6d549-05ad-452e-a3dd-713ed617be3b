using UnityEngine;

public class Dronecameraactivedeactive : MonoBehaviour
{
    public GameObject dronecameracanvas, playercanvas;
    public GameObject playercamera, dronecamera;
    public Transform DronePosition;

    // Reference to the Dronecamera script
    private Dronecamera droneCameraScript;

    void Start()
    {
        // Get the Dronecamera script component
        if (dronecamera != null)
        {
            droneCameraScript = dronecamera.GetComponent<Dronecamera>();
        }
    }

    public void PlayDrone()
    {
        dronecameracanvas.SetActive(true);
        playercanvas.SetActive(false);
        playercamera.SetActive(false);
        dronecamera.SetActive(true);
    }

    public void StopDrone()
    {
        dronecameracanvas.SetActive(false);
        playercanvas.SetActive(true);
        playercamera.SetActive(true);
        dronecamera.SetActive(false);

        // Reset drone camera position and rotation properly
        if (DronePosition != null && droneCameraScript != null)
        {
            // Use the Dronecamera script's methods to properly reset position and rotation
            droneCameraScript.SetCameraPosition(DronePosition.position);
            droneCameraScript.SetCameraRotation(DronePosition.eulerAngles);
        }
        else if (DronePosition != null)
        {
            // Fallback: direct transform manipulation if Dronecamera script is not available
            dronecamera.transform.position = DronePosition.position;
            dronecamera.transform.rotation = DronePosition.rotation;
        }
    }
}
