﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(Configuration)\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>UNITY_6000_0_30;UNITY_6000_0;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_ACCESSIBILITY;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_ANDROID;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;PLATFORM_HAS_ADDITIONAL_API_CHECKS;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;MOREMOUNTAINS_NICEVIBRATIONS;CROSS_PLATFORM_INPUT;MOBILE_INPUT;BCG_RCCP;BCG_RCC;UNITY_POST_PROCESSING_STACK_V2;NWH_WC3D;NWH_NVP2;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>Android:13</UnityBuildTarget>
    <UnityVersion>6000.0.30f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Tools\Unity.SourceGenerators\Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_BehaviorButton.cs" />
    <Compile Include="Assets\MSK 2.2\Scripts\BikeSwitch.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_TrailerAttachPoint.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\HelicopterSystemManager.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\GameCanvas.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Neon.cs" />
    <Compile Include="Assets\Game Script\Drive vehicle enter exit\Dronecameraactivedeactive.cs" />
    <Compile Include="Assets\JMO Assets\Cartoon FX Remaster\CFXR Assets\Scripts\CFXR_ParticleTextFontAsset.cs" />
    <Compile Include="Assets\JMO Assets\Cartoon FX Remaster\Demo Assets\CFXR_Demo_Rotate.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_DemoVehicles.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\Spline.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_CrashPress.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainSpawner.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Speed.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SMR_IgnoredObject.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Telemetry.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_SetLayer.cs" />
    <Compile Include="Assets\Invector-3rdPersonController_LITE\3D Models\Others\HealthItem\vAnimateUV.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Inputs.cs" />
    <Compile Include="Assets\UnityTechnologies\EffectExamples\Shared\Scripts\ParticleExamples.cs" />
    <Compile Include="Assets\Invector-3rdPersonController_LITE\Scripts\CharacterController\vThirdPersonMotor.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainSuspension.cs" />
    <Compile Include="Assets\Invector-3rdPersonController_LITE\Scripts\CharacterController\vThirdPersonInput.cs" />
    <Compile Include="Assets\MSK 2.2\Skidmarks\Skidmarks Essentials\Skidmarks.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\CustomWagonCreator.cs" />
    <Compile Include="Assets\Script\CameraManager.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\HelicopterController.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\ReverseDirectionZone.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\VFX.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\SpeedometerScript.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_CustomizationSetups.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_Spoiler.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_Loadout.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Joint.cs" />
    <Compile Include="Assets\Game Script\Drive vehicle enter exit\allcanvasfalse.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Installation.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_PoliceSiren.cs" />
    <Compile Include="Assets\JMO Assets\Cartoon FX Remaster\Demo Assets\Kino Bloom\Bloom.cs" />
    <Compile Include="Assets\Joystick Pack\Scripts\Joysticks\FloatingJoystick.cs" />
    <Compile Include="Assets\UnityTechnologies\EffectExamples\Shared\Scripts\ParticleCollision.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_ShowroomCameraDrag.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\FollowTargetCamera.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_Decal.cs" />
    <Compile Include="Assets\MSK 2.2\Scripts\BikeAnimation.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Singleton.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Probability.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Prop.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_InfoLabel.cs" />
    <Compile Include="Assets\Scripts\MobileUIController.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\StationStopTrigger.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_AIBrakeZone.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainDoorsController.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\HonkZone_v3.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\RadarSystem.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_PlayerPrefsX.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\WSM_TerrainBackup.cs" />
    <Compile Include="Assets\Invector-3rdPersonController_LITE\Scripts\Generic\Utils\vComment.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_APIExample.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_CheckUp.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\RailSensor.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Caliper.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_Neon.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\FollowTargetCamera.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\Convert.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_GroundMaterials.cs" />
    <Compile Include="Assets\JMO Assets\Welcome Screen\CFXR_WelcomeScreen.cs" />
    <Compile Include="Assets\Script\WheelManager.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Damage.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_SteeringWheelController.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\MissileScript.cs" />
    <Compile Include="Assets\JMO Assets\Cartoon FX Remaster\CFXR Assets\Scripts\CFXR_EmissionBySurface.cs" />
    <Compile Include="Assets\Game Script\Drive vehicle enter exit\Drivevehicleenterexit.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Joystick.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_AssetPaths.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_LevelLoader.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_InitialSettings.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_DecalManager.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\SplineBasedTrainCarCoupler.cs" />
    <Compile Include="Assets\Invector-3rdPersonController_LITE\Scripts\Generic\Utils\vExtensions.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_CrashShredder.cs" />
    <Compile Include="Assets\JMO Assets\Cartoon FX Remaster\Demo Assets\CFXR_Demo.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customization_API.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_PaintManager.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_WheelManager.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_BehaviorTester.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SMR_GeneratedCollider.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Extensions Interfaces\ITrainDoorsController.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\GeneralSettings.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\RouteManager.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SMR_MeshGenerationProfile.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\RadarTypeInfo.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainInputSettings.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\Gasoline.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainController_v3.cs" />
    <Compile Include="Assets\MSK 2.2\Scripts\BikeCamera.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\OrientedPoint.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Skidmarks.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\CustomWagonProfile.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\SplineBasedWagon.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\CollactableScript.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SMR_Enums.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_CrashHammer.cs" />
    <Compile Include="Assets\Scripts\UIButtonConnector.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainWheelsTruck.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\RadarItem.cs" />
    <Compile Include="Assets\Scripts\Dronecamera.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainSpeedMonitor.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SplineFollower.cs" />
    <Compile Include="Assets\UnityTechnologies\EffectExamples\Shared\Scripts\ExtinguishableFire.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainPlayerInput.cs" />
    <Compile Include="Assets\Script\Airplanecontroll.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_WheelCollider.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_HoodCamera.cs" />
    <Compile Include="Assets\JMO Assets\Cartoon FX Remaster\Demo Assets\CFXR_Demo_Translate.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_Paint.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\Bezier.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\CustomEventZone.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_SpoilerManager.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Siren.cs" />
    <Compile Include="Assets\Joystick Pack\Scripts\Joysticks\FixedJoystick.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_ChangableWheels.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Settings.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_AIWaypointsContainer.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\CustomWagonComponent.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_DemoMaterials.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\BulletScript.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_DashboardObjects.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SplineMeshRenderer.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\SimpleJoystick.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainAudio.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Camera\CameraSettings.cs" />
    <Compile Include="Assets\MSK 2.2\Scripts\GizmoObject.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_CustomizationDemo.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_WheelCamera.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SMR_Theme.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\GyroscopeViewController.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_CarSelectionExample.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_SpeedLimiter.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\RadarTypeInfo.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_CinematicCamera.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_SceneManager.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_BehaviorSelector.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_OverrideInputsExample.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Extensions Interfaces\ITrainCarCoupler.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Camera.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\EnemyAI.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_UpgradeManager.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_OctreeNode.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainCarCoupler.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_SuspensionArm.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SMR_MeshCapTag.cs" />
    <Compile Include="Assets\UnityTechnologies\EffectExamples\Shared\Scripts\ParticleMenu.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Teleporter.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SMR_GeneratedMesh.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Extensions Interfaces\ILocomotive.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_FixedCamera.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Sensors.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_SirenManager.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_Brake.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\GameCanvas.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Upgrade.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\GunController.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_ShowroomCamera.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_InputManager.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_ColorPickerBySliders.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\IGeneratedMesh.cs" />
    <Compile Include="Assets\UnityTechnologies\EffectExamples\Shared\Scripts\GunAim.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SplinePrefabSpawner.cs" />
    <Compile Include="Assets\MSK 2.2\Scripts\BikeControl.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Spawner.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Version.cs" />
    <Compile Include="Assets\Invector-3rdPersonController_LITE\Scripts\Generic\Utils\vPickupItem.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainStationController.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_GetBounds.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_CameraCarSelection.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_DashboardInputs.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Useless.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_Handling.cs" />
    <Compile Include="Assets\Joystick Pack\Scripts\Joysticks\DynamicJoystick.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Core.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Decal.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_DashboardColors.cs" />
    <Compile Include="Assets\MSK 2.2\Scripts\RearLookAt.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Camera\FlyingCamera.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\BoatEngineSound.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SMR_UISettings.cs" />
    <Compile Include="Assets\Game Script\Drive vehicle enter exit\VehiclePlayerPosition.cs" />
    <Compile Include="Assets\Joystick Pack\Scripts\Base\Joystick.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Emission.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\PassengerTags.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\WaterEffect.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Controller.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\SplineDefaultValues.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_NeonManager.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\RadarSystem.cs" />
    <Compile Include="Assets\Joystick Pack\Examples\JoystickPlayerExample.cs" />
    <Compile Include="Assets\Script\AirplaneCrah.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\SpawnPosition.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_Speed.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Camera\CameraFollow.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\MissileScript.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_FOVForCinematicCamera.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_CustomizationSlider.cs" />
    <Compile Include="Assets\JMO Assets\Cartoon FX Remaster\CFXR Assets\Scripts\CFXR_ParticleText.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_MobileButtons.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\SpeedChangeZone_v3.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainAttachPassenger.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Waypoint.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_DashboardDisplay.cs" />
    <Compile Include="Assets\MSK 2.2\Scripts\PoliceLights.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_MultipleBehaviorDemo.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_CustomizationManager.cs" />
    <Compile Include="Assets\UnityTechnologies\EffectExamples\Shared\Scripts\DecalDestroyer.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Route.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_InputActions.cs" />
    <Compile Include="Assets\Joystick Pack\Scripts\Joysticks\VariableJoystick.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_CharacterController.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_TruckTrailer.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_Engine.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Drag.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Recorder.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainPhysics.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_FuelStation.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Color.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Octree.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_DashboardButton.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\GyroscopeViewController.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\BoatSystemManager.cs" />
    <Compile Include="Assets\MSK 2.2\Skidmarks\WheelSkidmarks.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\RadarTargetType.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\SimpleJoystick.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_SkidmarksManager.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Exhaust.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\SFX.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Light.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\SplineBasedLocomotive.cs" />
    <Compile Include="Assets\Joystick Pack\Examples\JoystickSetterExample.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\DirectionIndicator.cs" />
    <Compile Include="Assets\Script\AeroplaneEngine.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\SwitchTrigger.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\BulletScript.cs" />
    <Compile Include="Assets\JMO Assets\Cartoon FX Remaster\CFXR Assets\Scripts\CFXR_Effect.CameraShake.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_CustomizationTrigger.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\WSM_TerrainTools.cs" />
    <Compile Include="Assets\Game Script\Drive vehicle enter exit\OtherVehicleEnterexit.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_ShadowRotConst.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\CollactableScript.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\EnemyAI.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\BakedSegment.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\UniqueMesh.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Extensions Interfaces\IRailwayVehicle.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\RadarTargetType.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\Gasoline.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TC_Enums.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_RepairStation.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\WaterSplashScript.cs" />
    <Compile Include="Assets\Invector-3rdPersonController_LITE\Scripts\Camera\vThirdPersonCamera.cs" />
    <Compile Include="Assets\Game Script\truckrcccam.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_LOD.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_AICarController.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainParticles.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\AnimationParameters.cs" />
    <Compile Include="Assets\UnityTechnologies\EffectExamples\TutorialInfo\Scripts\Readme.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainDoor.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainWheel_v3.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\RailroadSwitch_v3.cs" />
    <Compile Include="Assets\Invector-3rdPersonController_LITE\Scripts\CharacterController\vThirdPersonAnimator.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Canvas_Customizer.cs" />
    <Compile Include="Assets\Invector-3rdPersonController_LITE\Scripts\CharacterController\vThirdPersonController.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\BellZone.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\GunController.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_CustomizationManager.cs" />
    <Compile Include="Assets\UnityTechnologies\EffectExamples\Shared\Scripts\GunShoot.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_DecalSetLocation.cs" />
    <Compile Include="Assets\JMO Assets\Cartoon FX Remaster\CFXR Assets\Scripts\CFXR_Effect.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\DemoUI_v3.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\TrainProfile.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_CustomizationData.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_CarControllerV4.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Records.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\BoatController.cs" />
    <Compile Include="Assets\BoatControllerwithShooting\Scripts\NaturalAI.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\HeliRotorController.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Mirror.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\Customization\RCC_Customizer_Siren.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Scripts\Wagon_v3.cs" />
    <Compile Include="Assets\AdvancedHelicopterController\Scripts\RadarItem.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_AIBrakeZonesContainer.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Wheel.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_Spoiler.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_UI_SliderTextReader.cs" />
    <Compile Include="Assets\WSM Game Studio\Train Controller_v3\Railroad Builder\Scripts\LinkedSplineFollower.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_DetachablePart.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_Demo.cs" />
    <Compile Include="Assets\RealisticCarControllerV4\Scripts\RCC_AIO.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S Multiply Soft Scroll.shader" />
    <None Include="Assets\RealisticCarControllerV4\Addon Packages\For ProFlares\Readme!.txt" />
    <None Include="Assets\RealisticCarControllerV4\Shaders\Builtin\Builtin shaders will be stored here.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro.cginc" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\WSM Game Studio\Train Controller_v3\Addons\Cable Car Expansion Pack\Documentation\Readme.txt" />
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S Particle Add A8.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile-2-Pass.shader" />
    <None Include="Assets\BuildReport\CustomBuildScriptExample.txt" />
    <None Include="Assets\WSM Game Studio\Train Controller_v3\Prefabs\Deprecated\READ ME.txt" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S AlphaBlend Scroll.shader" />
    <None Include="Assets\MSK 2.2\Shaders\ReflectionBlurFresnel.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S Multiply Soft Scroll.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Mobile.cginc" />
    <None Include="Assets\RealisticCarControllerV4\Skybox\SkyboxGuidelines.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF SSD.shader" />
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S Smoke Scroll.shader" />
    <None Include="Assets\BuildReport\README.txt" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S Smoke Scroll.shader" />
    <None Include="Assets\WSM Game Studio\Train Controller_v3\Addons\Subway Expansion Pack\Documentation\Readme.txt" />
    <None Include="Assets\Invector-3rdPersonController_LITE\README_Basic.txt" />
    <None Include="Assets\BoatControllerwithShooting\MaterialsTextures\NW_Particle AddSmooth.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\MSK 2.2\Standard Assets\Projectors\Guidelines.txt" />
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S Add Scroll.shader" />
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S Particle AddSoft A8.shader" />
    <None Include="Assets\WSM Game Studio\Train Controller_v3\Shared\Models\Deprecated\READ ME.txt" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S Particle AddSoft A8.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\SDFFunctions.hlsl" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S Particle Add A8.shader" />
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S Alpha-Specular TintColor.shader" />
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S Alpha-Diffuse TintColor.shader" />
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S Multiply Soft.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S Add Scroll.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF.shader" />
    <None Include="Assets\RealisticCarControllerV4\LICENSE - Read Me!.txt" />
    <None Include="Assets\BuildReport\license.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S AlphaBlend NoSoftParticles.shader" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S Particle Multiply A8.shader" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S Alpha-Diffuse TintColor.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\BuildReport\VERSION.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S Multiply Soft.shader" />
    <None Include="Assets\WSM Game Studio\Train Controller_v3\Demo\READ ME.txt" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S AlphaBlend NoSoftParticles.shader" />
    <None Include="Assets\WSM Game Studio\Train Controller_v3\Documentation\Readme.txt" />
    <None Include="Assets\RealisticCarControllerV4\Shaders\RCC_Skidmarks.shader" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\WSM Game Studio\Train Controller_v3\Prefabs\READ ME - Addons and Extensions.txt" />
    <None Include="Assets\MSK 2.2\Standard Assets\Projectors\Sources\Shaders\Projector Light.shader" />
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S Particle Multiply A8.shader" />
    <None Include="Assets\MSK 2.2\Shaders\TransparentRef.shader" />
    <None Include="Assets\RealisticCarControllerV4\Shaders\Builtin\RCC_Shader_CarBody.shader" />
    <None Include="Assets\BoatControllerwithShooting\Models\Shaders\WFX_S Alpha-Specular TintColor.shader" />
    <None Include="Assets\AdvancedHelicopterController\Models\Shaders\WFX_S AlphaBlend Scroll.shader" />
    <None Include="Assets\MSK 2.2\Standard Assets\Projectors\Sources\Shaders\Projector Multiply.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\MSK 2.2\Skidmarks\Skidmarks Essentials\Skidmarks.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\UnityTechnologies\EffectExamples\Shared\Shaders\SurfaceShader_VC.shader" />
    <None Include="Assets\WSM Game Studio\Train Controller_v3\Addons\Steam Locomotive Expansion Pack\Documentation\Readme.txt" />
    <None Include="Assets\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\TextMesh Pro\Shaders\TMP_Sprite.shader" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\Managed\UnityEngine\UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>Library\PackageCache\com.unity.testtools.codecoverage\lib\ReportGenerator\ReportGeneratorMerged.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.visualscripting\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem.ForUI">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.ForUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Searcher.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Cinemachine.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Cinemachine.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime.Shared">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ShaderGraph.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TestTools.CodeCoverage.Editor.OpenCover.Model">
      <HintPath>Library\ScriptAssemblies\Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor.Shared">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TestTools.CodeCoverage.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TestTools.CodeCoverage.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Multiplayer.Center.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Splines">
      <HintPath>Library\ScriptAssemblies\Unity.Splines.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Performance.Profile-Analyzer.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Performance.Profile-Analyzer.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.GPUDriven.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.GPUDriven.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Cinemachine">
      <HintPath>Library\ScriptAssemblies\Unity.Cinemachine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rendering.LightTransport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Splines.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Splines.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Shared.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Shared.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Common">
      <HintPath>Library\ScriptAssemblies\Unity.Multiplayer.Center.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection">
      <HintPath>Library\ScriptAssemblies\Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Flow.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Postprocessing.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Postprocessing.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.EditorCoroutines.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.EditorCoroutines.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.SettingsProvider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.SettingsProvider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.State.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.State.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Postprocessing.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Postprocessing.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Flow.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.Rendering.LightTransport.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj" />
    <ProjectReference Include="BuildReportTool.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
