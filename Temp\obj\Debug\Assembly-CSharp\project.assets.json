{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"BuildReportTool": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}}, "BuildReportTool/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/BuildReportTool.dll": {}}, "runtime": {"bin/placeholder/BuildReportTool.dll": {}}}}}, "libraries": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "path": "Assembly-CSharp-firstpass.csproj", "msbuildProject": "Assembly-CSharp-firstpass.csproj"}, "BuildReportTool/1.0.0": {"type": "project", "path": "BuildReportTool.csproj", "msbuildProject": "BuildReportTool.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp-firstpass >= 1.0.0", "BuildReportTool >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\My Project\\Driving Simulator Game Z TEC\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\My Project\\Driving Simulator Game Z TEC\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\My Project\\Driving Simulator Game Z TEC\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\My Project\\Driving Simulator Game Z TEC\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\My Project\\Driving Simulator Game Z TEC\\Assembly-CSharp-firstpass.csproj"}, "D:\\My Project\\Driving Simulator Game Z TEC\\BuildReportTool.csproj": {"projectPath": "D:\\My Project\\Driving Simulator Game Z TEC\\BuildReportTool.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}