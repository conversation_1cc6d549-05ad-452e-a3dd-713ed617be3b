{"BeeBuildProgramCommon.Data.ConfigurationData": {"Il2CppDir": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\il2cpp", "UnityLinkerPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\il2cpp/build/deploy/UnityLinker.exe", "Il2CppPath": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\il2cpp/build/deploy/il2cpp.exe", "NetCoreRunPath": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data\\Tools\\netcorerun\\netcorerun.exe", "DotNetExe": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/NetCoreRuntime/dotnet.exe", "EditorContentsPath": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data", "Packages": [{"Name": "com.unity.cinemachine", "ResolvedPath": "Library/PackageCache/com.unity.cinemachine"}, {"Name": "com.unity.collab-proxy", "ResolvedPath": "Library/PackageCache/com.unity.collab-proxy"}, {"Name": "com.unity.editorcoroutines", "ResolvedPath": "Library/PackageCache/com.unity.editorcoroutines"}, {"Name": "com.unity.ext.nunit", "ResolvedPath": "Library/PackageCache/com.unity.ext.nunit"}, {"Name": "com.unity.feature.development", "ResolvedPath": "Library/PackageCache/com.unity.feature.development"}, {"Name": "com.unity.ide.rider", "ResolvedPath": "Library/PackageCache/com.unity.ide.rider"}, {"Name": "com.unity.ide.visualstudio", "ResolvedPath": "Library/PackageCache/com.unity.ide.visualstudio"}, {"Name": "com.unity.inputsystem", "ResolvedPath": "Library/PackageCache/com.unity.inputsystem"}, {"Name": "com.unity.multiplayer.center", "ResolvedPath": "Library/PackageCache/com.unity.multiplayer.center"}, {"Name": "com.unity.nuget.newtonsoft-json", "ResolvedPath": "Library/PackageCache/com.unity.nuget.newtonsoft-json"}, {"Name": "com.unity.performance.profile-analyzer", "ResolvedPath": "Library/PackageCache/com.unity.performance.profile-analyzer"}, {"Name": "com.unity.postprocessing", "ResolvedPath": "Library/PackageCache/com.unity.postprocessing"}, {"Name": "com.unity.settings-manager", "ResolvedPath": "Library/PackageCache/com.unity.settings-manager"}, {"Name": "com.unity.shadergraph", "ResolvedPath": "Library/PackageCache/com.unity.shadergraph"}, {"Name": "com.unity.test-framework", "ResolvedPath": "Library/PackageCache/com.unity.test-framework"}, {"Name": "com.unity.testtools.codecoverage", "ResolvedPath": "Library/PackageCache/com.unity.testtools.codecoverage"}, {"Name": "com.unity.timeline", "ResolvedPath": "Library/PackageCache/com.unity.timeline"}, {"Name": "com.unity.ugui", "ResolvedPath": "Library/PackageCache/com.unity.ugui"}, {"Name": "com.unity.visualscripting", "ResolvedPath": "Library/PackageCache/com.unity.visualscripting"}, {"Name": "com.unity.modules.accessibility", "ResolvedPath": "Library/PackageCache/com.unity.modules.accessibility"}, {"Name": "com.unity.modules.ai", "ResolvedPath": "Library/PackageCache/com.unity.modules.ai"}, {"Name": "com.unity.modules.androidjni", "ResolvedPath": "Library/PackageCache/com.unity.modules.androidjni"}, {"Name": "com.unity.modules.animation", "ResolvedPath": "Library/PackageCache/com.unity.modules.animation"}, {"Name": "com.unity.modules.assetbundle", "ResolvedPath": "Library/PackageCache/com.unity.modules.assetbundle"}, {"Name": "com.unity.modules.audio", "ResolvedPath": "Library/PackageCache/com.unity.modules.audio"}, {"Name": "com.unity.modules.cloth", "ResolvedPath": "Library/PackageCache/com.unity.modules.cloth"}, {"Name": "com.unity.modules.director", "ResolvedPath": "Library/PackageCache/com.unity.modules.director"}, {"Name": "com.unity.modules.hierarchycore", "ResolvedPath": "Library/PackageCache/com.unity.modules.hierarchycore"}, {"Name": "com.unity.modules.imageconversion", "ResolvedPath": "Library/PackageCache/com.unity.modules.imageconversion"}, {"Name": "com.unity.modules.imgui", "ResolvedPath": "Library/PackageCache/com.unity.modules.imgui"}, {"Name": "com.unity.modules.jsonserialize", "ResolvedPath": "Library/PackageCache/com.unity.modules.jsonserialize"}, {"Name": "com.unity.modules.particlesystem", "ResolvedPath": "Library/PackageCache/com.unity.modules.particlesystem"}, {"Name": "com.unity.modules.physics", "ResolvedPath": "Library/PackageCache/com.unity.modules.physics"}, {"Name": "com.unity.modules.physics2d", "ResolvedPath": "Library/PackageCache/com.unity.modules.physics2d"}, {"Name": "com.unity.modules.screencapture", "ResolvedPath": "Library/PackageCache/com.unity.modules.screencapture"}, {"Name": "com.unity.modules.subsystems", "ResolvedPath": "Library/PackageCache/com.unity.modules.subsystems"}, {"Name": "com.unity.modules.terrain", "ResolvedPath": "Library/PackageCache/com.unity.modules.terrain"}, {"Name": "com.unity.modules.terrainphysics", "ResolvedPath": "Library/PackageCache/com.unity.modules.terrainphysics"}, {"Name": "com.unity.modules.tilemap", "ResolvedPath": "Library/PackageCache/com.unity.modules.tilemap"}, {"Name": "com.unity.modules.ui", "ResolvedPath": "Library/PackageCache/com.unity.modules.ui"}, {"Name": "com.unity.modules.uielements", "ResolvedPath": "Library/PackageCache/com.unity.modules.uielements"}, {"Name": "com.unity.modules.umbra", "ResolvedPath": "Library/PackageCache/com.unity.modules.umbra"}, {"Name": "com.unity.modules.unityanalytics", "ResolvedPath": "Library/PackageCache/com.unity.modules.unityanalytics"}, {"Name": "com.unity.modules.unitywebrequest", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequest"}, {"Name": "com.unity.modules.unitywebrequestassetbundle", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequestassetbundle"}, {"Name": "com.unity.modules.unitywebrequestaudio", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequestaudio"}, {"Name": "com.unity.modules.unitywebrequesttexture", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequesttexture"}, {"Name": "com.unity.modules.unitywebrequestwww", "ResolvedPath": "Library/PackageCache/com.unity.modules.unitywebrequestwww"}, {"Name": "com.unity.modules.vehicles", "ResolvedPath": "Library/PackageCache/com.unity.modules.vehicles"}, {"Name": "com.unity.modules.video", "ResolvedPath": "Library/PackageCache/com.unity.modules.video"}, {"Name": "com.unity.modules.vr", "ResolvedPath": "Library/PackageCache/com.unity.modules.vr"}, {"Name": "com.unity.modules.wind", "ResolvedPath": "Library/PackageCache/com.unity.modules.wind"}, {"Name": "com.unity.modules.xr", "ResolvedPath": "Library/PackageCache/com.unity.modules.xr"}, {"Name": "com.unity.render-pipelines.core", "ResolvedPath": "Library/PackageCache/com.unity.render-pipelines.core"}, {"Name": "com.unity.searcher", "ResolvedPath": "Library/PackageCache/com.unity.searcher"}, {"Name": "com.unity.splines", "ResolvedPath": "Library/PackageCache/com.unity.splines"}, {"Name": "com.unity.burst", "ResolvedPath": "Library/PackageCache/com.unity.burst"}, {"Name": "com.unity.mathematics", "ResolvedPath": "Library/PackageCache/com.unity.mathematics"}, {"Name": "com.unity.collections", "ResolvedPath": "Library/PackageCache/com.unity.collections"}, {"Name": "com.unity.rendering.light-transport", "ResolvedPath": "Library/PackageCache/com.unity.rendering.light-transport"}, {"Name": "com.unity.nuget.mono-cecil", "ResolvedPath": "Library/PackageCache/com.unity.nuget.mono-cecil"}, {"Name": "com.unity.test-framework.performance", "ResolvedPath": "Library/PackageCache/com.unity.test-framework.performance"}], "UnityVersion": "6000.0.30f1", "UnityVersionNumeric": {"Release": 6000, "Major": 0, "Minor": 30}, "Batchmode": false, "EmitDataForBeeWhy": false, "NamedPipeOrUnixSocket": "unity-ilpp-26f7ee93bb6df090be79e5a29c8cf577"}, "ScriptCompilationBuildProgram.Data.ScriptCompilationData": {"Assemblies": [], "DotnetRuntimePath": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/NetCoreRuntime", "DotnetRoslynPath": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/DotNetSdkRoslyn", "MovedFromExtractorPath": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/Compilation/ApiUpdater/ApiUpdater.MovedFromExtractor.dll", "OutputDirectory": "Library/ScriptAssemblies", "Debug": false, "BuildTarget": "Android", "Localization": "en-US", "BuildPlayerDataOutput": "Library/BuildPlayerData/Editor", "ExtractRuntimeInitializeOnLoads": false, "EnableDiagnostics": false, "EmitInfoForScriptUpdater": true, "AssembliesToScanForTypeDB": ["Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll", "Assets/Plugins/Demigiant/DOTween/DOTween.dll", "Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll", "Library/ScriptAssemblies/Assembly-CSharp.dll", "Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll", "Library/ScriptAssemblies/Unity.Burst.dll", "Library/ScriptAssemblies/Unity.Cinemachine.dll", "Library/ScriptAssemblies/Unity.Collections.dll", "Library/ScriptAssemblies/Unity.InputSystem.dll", "Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll", "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll", "Library/ScriptAssemblies/Unity.Mathematics.dll", "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll", "Library/ScriptAssemblies/Unity.PerformanceTesting.dll", "Library/ScriptAssemblies/Unity.Postprocessing.Runtime.dll", "Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Library/ScriptAssemblies/Unity.Splines.dll", "Library/ScriptAssemblies/Unity.TextMeshPro.dll", "Library/ScriptAssemblies/Unity.Timeline.dll", "Library/ScriptAssemblies/Unity.VisualScripting.Core.dll", "Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll", "Library/ScriptAssemblies/Unity.VisualScripting.State.dll", "Library/ScriptAssemblies/UnityEngine.TestRunner.dll", "Library/ScriptAssemblies/UnityEngine.UI.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll", "Packages/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll", "Packages/com.unity.burst/Unity.Burst.Unsafe.dll", "Packages/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll", "Packages/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.Mdb.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.Pdb.dll", "Packages/com.unity.nuget.mono-cecil/Mono.Cecil.Rocks.dll", "Packages/com.unity.nuget.newtonsoft-json/Runtime/Newtonsoft.Json.dll", "Packages/com.unity.testtools.codecoverage/lib/ReportGenerator/ReportGeneratorMerged.dll", "Packages/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"], "SearchPaths": ["Assets/Plugins/Demigiant/DemiLib/Core", "Assets/Plugins/Demigiant/DOTween", "Assets/Plugins/Demigiant/DOTweenPro", "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Managed\\UnityEngine", "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api", "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades", "Library/ScriptAssemblies", "Packages/com.unity.burst", "Packages/com.unity.burst/Unity.Burst.CodeGen", "Packages/com.unity.collections/Unity.Collections.LowLevel.ILSupport", "Packages/com.unity.ext.nunit/net40/unity-custom", "Packages/com.unity.nuget.mono-cecil", "Packages/com.unity.nuget.newtonsoft-json/Runtime", "Packages/com.unity.testtools.codecoverage/lib/ReportGenerator", "Packages/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc"]}}