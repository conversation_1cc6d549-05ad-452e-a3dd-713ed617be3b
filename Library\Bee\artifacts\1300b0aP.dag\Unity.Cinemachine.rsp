-target:library
-out:"Library/Bee/artifacts/1300b0aP.dag/Unity.Cinemachine.dll"
-refout:"Library/Bee/artifacts/1300b0aP.dag/Unity.Cinemachine.ref.dll"
-define:UNITY_6000_0_30
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_ACCESSIBILITY
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_ANDROID
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:PLATFORM_HAS_ADDITIONAL_API_CHECKS
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_IL2CPP
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:MOREMOUNTAINS_NICEVIBRATIONS
-define:CROSS_PLATFORM_INPUT
-define:MOBILE_INPUT
-define:BCG_RCCP
-define:BCG_RCC
-define:UNITY_POST_PROCESSING_STACK_V2
-define:NWH_WC3D
-define:NWH_NVP2
-define:CINEMACHINE_POST_PROCESSING_V2
-define:CINEMACHINE_TIMELINE
-define:CINEMACHINE_PHYSICS_2D
-define:CINEMACHINE_PHYSICS
-define:CINEMACHINE_UGUI
-define:CINEMACHINE_UNITY_INPUTSYSTEM
-define:CINEMACHINE_UNITY_ANIMATION
-define:CINEMACHINE_UIELEMENTS
-define:CINEMACHINE_SPLINES_2_4
-define:CINEMACHINE_UNITY_6000_0_11F1_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Managed/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json/Runtime/AOT/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.Postprocessing.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.Splines.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Library/PackageCache/com.unity.cinemachine/Runtime/AssemblyInfo.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineBrain.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineCamera.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineCameraOffset.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineClearShot.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineConfiner2D.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineConfiner3D.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineDecollider.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineDeoccluder.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineExternalCamera.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineFollowZoom.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineFreeLookModifier.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineGroupFraming.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineMixingCamera.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachinePixelPerfect.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineRecomposer.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineSequencerCamera.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineShotQualityEvaluator.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineSplineCart.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineSplineRoll.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineStateDrivenCamera.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineStoryboard.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineTargetGroup.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Behaviours/CinemachineThirdPersonAim.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachineBasicMultiChannelPerlin.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachineFollow.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachineHardLockToTarget.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachineHardLookAt.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachineOrbitalFollow.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachinePanTilt.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachinePositionComposer.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachineRotateWithFollowTarget.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachineRotationComposer.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachineSplineDolly.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachineSplineDollyLookAtTargets.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Components/CinemachineThirdPersonFollow.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/BlendManager.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CameraBlendStack.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CameraState.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CameraTarget.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CameraUpdateManager.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CinemachineBlend.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CinemachineBlenderSettings.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CinemachineCameraManagerBase.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CinemachineComponentBase.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CinemachineCore.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CinemachineExtension.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CinemachinePropertyAttribute.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/CinemachineVirtualCameraBase.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/ConfinerOven.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/DeltaTimeScaleProcessor.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/GaussianFilter.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/ICinemachineCamera.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/InputAxis.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/InputAxisControllerBase.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/IShotQualityEvaluator.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/LensSettings.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/LookaheadSettings.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/NoiseSettings.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/OutputChannel.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/Predictor.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/PrioritySettings.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/RuntimeUtility.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/ScreenComposerSettings.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/SignalSourceAsset.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/SplineAutoDolly.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/SplineContainerExtensions.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/SplineHelpers.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/SplineSettings.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/TargetPositionCache.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/TargetTracking.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/UnityVectorExtensions.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/UpdateTracker.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Core/VirtualCameraRegistry.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Debug/CinemachineDebug.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Debug/DebugText.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/AxisState.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/Cinemachine3rdPersonFollow.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineCollider.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineComposer.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineConfiner.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineDollyCart.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineDoNotUpgrade.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineFramingTransposer.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineFreeLook.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineGroupComposer.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineInputAxisDriver.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineInputProvider.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineLegacyCameraEvents.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineOrbitalTransposer.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachinePath.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachinePathBase.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachinePipeline.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachinePOV.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineSameAsFollowTarget.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineSmoothPath.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineTouchInputMapper.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineTrackedDolly.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineTransposer.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/CinemachineVirtualCamera.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/DeprecatedAttributes.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Deprecated/LegacyLensSettings.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Helpers/CinemachineBrainEvents.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Helpers/CinemachineCameraEvents.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Helpers/CinemachineCameraManagerEvents.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Helpers/CinemachineInputAxisController.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Helpers/CinemachineMixerEventsBase.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Helpers/CinemachineSplineSmoother.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Helpers/CinemachineTriggerAction.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Helpers/GroupWeightManipulator.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Impulse/CinemachineCollisionImpulseSource.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Impulse/CinemachineFixedSignal.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Impulse/CinemachineImpulseDefinition.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Impulse/CinemachineImpulseListener.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Impulse/CinemachineImpulseManager.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Impulse/CinemachineImpulseSource.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Impulse/CinemachinExternalImpulseListener.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/PostProcessing/CinemachineAutoFocus.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/PostProcessing/CinemachinePostProcessing.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/PostProcessing/CinemachineVolumeSettings.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/PostProcessing/FocusDistance.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/SaveDuringPlay/Attributes.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/ThirdParty/Clipper.Core.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/ThirdParty/clipper.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/ThirdParty/Clipper.Engine.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/ThirdParty/Clipper.Minkowski.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/ThirdParty/Clipper.Offset.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Timeline/CinemachinePlayableMixer.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Timeline/CinemachineShot.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Timeline/CinemachineShotPlayable.cs"
"Library/PackageCache/com.unity.cinemachine/Runtime/Timeline/CinemachineTrack.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
-warn:0
/additionalfile:"Library/Bee/artifacts/1300b0aP.dag/Unity.Cinemachine.UnityAdditionalFile.txt"