<?xml version="1.0" encoding="utf-8"?>
<BuildReportToolOptions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <BuildReportFolderName>UnityBuildReports</BuildReportFolderName>
  <SaveType>0</SaveType>
  <KeepCopyOfLogOfLastSuccessfulBuild>true</KeepCopyOfLogOfLastSuccessfulBuild>
  <CollectBuildInfo>true</CollectBuildInfo>
  <CalculateAssetDependencies>true</CalculateAssetDependencies>
  <CalculateAssetDependenciesOnUnusedToo>false</CalculateAssetDependenciesOnUnusedToo>
  <CollectTextureImportSettings>true</CollectTextureImportSettings>
  <CollectTextureImportSettingsOnUnusedToo>false</CollectTextureImportSettingsOnUnusedToo>
  <CollectMeshData>true</CollectMeshData>
  <CollectMeshDataOnUnusedToo>false</CollectMeshDataOnUnusedToo>
  <CollectPrefabData>true</CollectPrefabData>
  <GetProjectSettings>true</GetProjectSettings>
  <IncludeUsedAssetsInReportCreation>true</IncludeUsedAssetsInReportCreation>
  <IncludeUnusedAssetsInReportCreation>true</IncludeUnusedAssetsInReportCreation>
  <IncludeUnusedPrefabsInReportCreation>true</IncludeUnusedPrefabsInReportCreation>
  <IncludeBuildSizeInReportCreation>true</IncludeBuildSizeInReportCreation>
  <IncludeSvnInUnused>true</IncludeSvnInUnused>
  <IncludeGitInUnused>true</IncludeGitInUnused>
  <IncludeBuildReportToolAssetsInUnused>true</IncludeBuildReportToolAssetsInUnused>
  <GetSizeBeforeBuildForUsedAssets>true</GetSizeBeforeBuildForUsedAssets>
  <GetImportedSizesForUnusedAssets>true</GetImportedSizesForUnusedAssets>
  <IgnorePatternsForUnused />
  <FileFilterNameForTextureData>Textures</FileFilterNameForTextureData>
  <ShowTextureColumnTextureType>true</ShowTextureColumnTextureType>
  <ShowTextureColumnIsSRGB>false</ShowTextureColumnIsSRGB>
  <ShowTextureColumnAlphaSource>false</ShowTextureColumnAlphaSource>
  <ShowTextureColumnAlphaIsTransparency>false</ShowTextureColumnAlphaIsTransparency>
  <ShowTextureColumnIgnorePngGamma>false</ShowTextureColumnIgnorePngGamma>
  <ShowTextureColumnNPotScale>false</ShowTextureColumnNPotScale>
  <ShowTextureColumnIsReadable>false</ShowTextureColumnIsReadable>
  <ShowTextureColumnMipMapGenerated>false</ShowTextureColumnMipMapGenerated>
  <ShowTextureColumnMipMapFilter>false</ShowTextureColumnMipMapFilter>
  <ShowTextureColumnStreamingMipMaps>false</ShowTextureColumnStreamingMipMaps>
  <ShowTextureColumnBorderMipMaps>false</ShowTextureColumnBorderMipMaps>
  <ShowTextureColumnPreserveCoverageMipMaps>false</ShowTextureColumnPreserveCoverageMipMaps>
  <ShowTextureColumnFadeOutMipMaps>false</ShowTextureColumnFadeOutMipMaps>
  <ShowTextureColumnSpriteImportMode>false</ShowTextureColumnSpriteImportMode>
  <ShowTextureColumnSpritePackingTag>false</ShowTextureColumnSpritePackingTag>
  <ShowTextureColumnSpritePixelsPerUnit>false</ShowTextureColumnSpritePixelsPerUnit>
  <ShowTextureColumnQualifiesForSpritePacking>false</ShowTextureColumnQualifiesForSpritePacking>
  <ShowTextureColumnWrapMode>false</ShowTextureColumnWrapMode>
  <ShowTextureColumnWrapModeU>false</ShowTextureColumnWrapModeU>
  <ShowTextureColumnWrapModeV>false</ShowTextureColumnWrapModeV>
  <ShowTextureColumnWrapModeW>false</ShowTextureColumnWrapModeW>
  <ShowTextureColumnFilterMode>false</ShowTextureColumnFilterMode>
  <ShowTextureColumnAnisoLevel>false</ShowTextureColumnAnisoLevel>
  <ShowTextureColumnMaxTextureSize>false</ShowTextureColumnMaxTextureSize>
  <ShowTextureColumnResizeAlgorithm>false</ShowTextureColumnResizeAlgorithm>
  <ShowTextureColumnTextureFormat>true</ShowTextureColumnTextureFormat>
  <ShowTextureColumnCompressionType>false</ShowTextureColumnCompressionType>
  <ShowTextureColumnCompressionIsCrunched>false</ShowTextureColumnCompressionIsCrunched>
  <ShowTextureColumnCompressionQuality>false</ShowTextureColumnCompressionQuality>
  <ShowTextureColumnImportedWidthAndHeight>true</ShowTextureColumnImportedWidthAndHeight>
  <ShowTextureColumnRealWidthAndHeight>false</ShowTextureColumnRealWidthAndHeight>
  <FileFilterNameForMeshData>Models</FileFilterNameForMeshData>
  <ShowMeshColumnMeshFilterCount>false</ShowMeshColumnMeshFilterCount>
  <ShowMeshColumnSkinnedMeshRendererCount>false</ShowMeshColumnSkinnedMeshRendererCount>
  <ShowMeshColumnSubMeshCount>true</ShowMeshColumnSubMeshCount>
  <ShowMeshColumnVertexCount>false</ShowMeshColumnVertexCount>
  <ShowMeshColumnTriangleCount>true</ShowMeshColumnTriangleCount>
  <ShowMeshColumnAnimationType>false</ShowMeshColumnAnimationType>
  <ShowMeshColumnAnimationClipCount>true</ShowMeshColumnAnimationClipCount>
  <FileFilterNameForPrefabData>Prefabs</FileFilterNameForPrefabData>
  <ShowPrefabColumnContributeGI>false</ShowPrefabColumnContributeGI>
  <ShowPrefabColumnBatchingStatic>true</ShowPrefabColumnBatchingStatic>
  <ShowPrefabColumnOccluderStatic>false</ShowPrefabColumnOccluderStatic>
  <ShowPrefabColumnOccludeeStatic>false</ShowPrefabColumnOccludeeStatic>
  <ShowPrefabColumnReflectionProbeStatic>false</ShowPrefabColumnReflectionProbeStatic>
  <ShowPrefabColumnNavigationStatic>false</ShowPrefabColumnNavigationStatic>
  <ShowPrefabColumnOffMeshLinkGeneration>false</ShowPrefabColumnOffMeshLinkGeneration>
  <ShowColumnAssetPath>true</ShowColumnAssetPath>
  <ShowColumnSizeBeforeBuild>true</ShowColumnSizeBeforeBuild>
  <ShowColumnSizeInBuild>true</ShowColumnSizeInBuild>
  <ShowColumnUnusedRawSize>true</ShowColumnUnusedRawSize>
  <ShowColumnUnusedImportedSize>true</ShowColumnUnusedImportedSize>
  <SearchType>100</SearchType>
  <SearchFilenameOnly>true</SearchFilenameOnly>
  <SearchCaseSensitive>false</SearchCaseSensitive>
  <ShowProjectSettingsInMultipleColumns>true</ShowProjectSettingsInMultipleColumns>
  <LogMessagePaginationLength>100</LogMessagePaginationLength>
  <FilterToUseInt>0</FilterToUseInt>
  <AssetListPaginationLength>300</AssetListPaginationLength>
  <ProcessUnusedAssetsInBatches>true</ProcessUnusedAssetsInBatches>
  <UnusedAssetsEntriesPerBatch>10000</UnusedAssetsEntriesPerBatch>
  <DoubleClickOnAssetWillPing>false</DoubleClickOnAssetWillPing>
  <AssetUsageLabelType>0</AssetUsageLabelType>
  <ShowAssetPrimaryUsersInTooltipIfAvailable>true</ShowAssetPrimaryUsersInTooltipIfAvailable>
  <ShowTooltipThumbnail>true</ShowTooltipThumbnail>
  <ShowThumbnailOnHoverType>0</ShowThumbnailOnHoverType>
  <TooltipThumbnailWidth>256</TooltipThumbnailWidth>
  <TooltipThumbnailHeight>256</TooltipThumbnailHeight>
  <TooltipThumbnailZoomedInWidth>512</TooltipThumbnailZoomedInWidth>
  <TooltipThumbnailZoomedInHeight>512</TooltipThumbnailZoomedInHeight>
  <NumberOfTopLargestUsedAssetsToShow>10</NumberOfTopLargestUsedAssetsToShow>
  <NumberOfTopLargestUnusedAssetsToShow>10</NumberOfTopLargestUnusedAssetsToShow>
  <AllowDeletingOfUsedAssets>false</AllowDeletingOfUsedAssets>
  <ShowImportedSizeForUsedAssets>false</ShowImportedSizeForUsedAssets>
  <AutoShowWindowAfterNormalBuild>true</AutoShowWindowAfterNormalBuild>
  <AutoResortAssetsWhenUnityEditorRegainsFocus>false</AutoResortAssetsWhenUnityEditorRegainsFocus>
  <UseThreadedReportGeneration>true</UseThreadedReportGeneration>
  <UseThreadedFileLoading>false</UseThreadedFileLoading>
</BuildReportToolOptions>