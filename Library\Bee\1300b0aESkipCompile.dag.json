{"Nodes": [{"Annotation": "all_tundra_nodes", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [1, 2, 3], "DebugActionIndex": 0}, {"Annotation": "ScriptAssemblies", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "DebugActionIndex": 1}, {"Annotation": "BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json", "DisplayName": "Extracting script serialization layouts", "Action": "\"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data\\Tools\\netcorerun\\netcorerun.exe\" \"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPlayerDataGenerator/BuildPlayerDataGenerator.exe\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Assets\\Plugins\\Demigiant\\DemiLib\\Core\\DemiLib.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Assets\\Plugins\\Demigiant\\DOTween\\DOTween.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Assets\\Plugins\\Demigiant\\DOTweenPro\\DOTweenPro.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Assembly-CSharp.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Assembly-CSharp-firstpass.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.Burst.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.Cinemachine.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.Collections.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.InputSystem.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.InputSystem.ForUI.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.InputSystem.TestFramework.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.Mathematics.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.Multiplayer.Center.Common.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.PerformanceTesting.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.Postprocessing.Runtime.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.Splines.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.TextMeshPro.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.Timeline.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.VisualScripting.Core.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.VisualScripting.Flow.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\Unity.VisualScripting.State.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\UnityEngine.TestRunner.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\\UnityEngine.UI.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.burst\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.burst\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.burst\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.burst\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.burst\\Unity.Burst.Unsafe.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.collections\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.ext.nunit\\net40\\unity-custom\\nunit.framework.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.nuget.mono-cecil\\Mono.Cecil.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.nuget.mono-cecil\\Mono.Cecil.Mdb.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.nuget.mono-cecil\\Mono.Cecil.Pdb.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.nuget.mono-cecil\\Mono.Cecil.Rocks.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json\\Runtime\\Newtonsoft.Json.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.testtools.codecoverage\\lib\\ReportGenerator\\ReportGeneratorMerged.dll\" -a=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Assets\\Plugins\\Demigiant\\DemiLib\\Core\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Assets\\Plugins\\Demigiant\\DOTween\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Assets\\Plugins\\Demigiant\\DOTweenPro\" -s=\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\Managed\\UnityEngine\" -s=\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\" -s=\"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\ScriptAssemblies\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.burst\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.burst\\Unity.Burst.CodeGen\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.collections\\Unity.Collections.LowLevel.ILSupport\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.ext.nunit\\net40\\unity-custom\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.nuget.mono-cecil\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.nuget.newtonsoft-json\\Runtime\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.testtools.codecoverage\\lib\\ReportGenerator\" -s=\"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\" -o=\"Library/BuildPlayerData/Editor\" -rn=\"\" -tn=\"TypeDb-All.json\"", "Inputs": ["C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/netcorerun/netcorerun.exe", "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPlayerDataGenerator/BuildPlayerDataGenerator.exe", "Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll", "Assets/Plugins/Demigiant/DOTween/DOTween.dll", "Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll", "Library/ScriptAssemblies/Assembly-CSharp.dll", "Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll", "Library/ScriptAssemblies/Unity.Burst.dll", "Library/ScriptAssemblies/Unity.Cinemachine.dll", "Library/ScriptAssemblies/Unity.Collections.dll", "Library/ScriptAssemblies/Unity.InputSystem.dll", "Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll", "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll", "Library/ScriptAssemblies/Unity.Mathematics.dll", "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll", "Library/ScriptAssemblies/Unity.PerformanceTesting.dll", "Library/ScriptAssemblies/Unity.Postprocessing.Runtime.dll", "Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll", "Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Library/ScriptAssemblies/Unity.Splines.dll", "Library/ScriptAssemblies/Unity.TextMeshPro.dll", "Library/ScriptAssemblies/Unity.Timeline.dll", "Library/ScriptAssemblies/Unity.VisualScripting.Core.dll", "Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll", "Library/ScriptAssemblies/Unity.VisualScripting.State.dll", "Library/ScriptAssemblies/UnityEngine.TestRunner.dll", "Library/ScriptAssemblies/UnityEngine.UI.dll", "Library/PackageCache/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll", "Library/PackageCache/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll", "Library/PackageCache/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll", "Library/PackageCache/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll", "Library/PackageCache/com.unity.burst/Unity.Burst.Unsafe.dll", "Library/PackageCache/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll", "Library/PackageCache/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll", "Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.dll", "Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.Mdb.dll", "Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.Pdb.dll", "Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.Rocks.dll", "Library/PackageCache/com.unity.nuget.newtonsoft-json/Runtime/Newtonsoft.Json.dll", "Library/PackageCache/com.unity.testtools.codecoverage/lib/ReportGenerator/ReportGeneratorMerged.dll", "Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"], "InputFlags": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "Outputs": ["Library/BuildPlayerData/Editor/TypeDb-All.json"], "OutputFlags": [0], "AllowUnexpectedOutput": true, "Env": [], "DebugActionIndex": 2}, {"Annotation": "ScriptAssembliesAndTypeDB", "DisplayName": null, "Inputs": [], "InputFlags": [], "Outputs": [], "OutputFlags": [], "ToBuildDependencies": [1, 2], "DebugActionIndex": 3}], "FileSignatures": [{"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.BuildTools.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.Core.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.CSharpSupport.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.DotNet.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.NativeProgramSupport.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.Stevedore.Program.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.TinyProfiler2.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.GNU.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.LLVM.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.VisualStudio.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.Toolchain.Xcode.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.Tools.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.TundraBackend.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Bee.VisualStudioSolution.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/BeeBuildProgramCommon.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/BeeBuildProgramCommon.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/BeeLocalCacheTool.exe"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Newtonsoft.Json.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/NiceIO.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/PlayerBuildProgramLibrary.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/PlayerBuildProgramLibrary.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/ScriptCompilationBuildProgram.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/ScriptCompilationBuildProgram.exe"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/SharpYaml.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Unity.Api.Attributes.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Mdb.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Pdb.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Unity.Cecil.Rocks.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Api.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Bee.BuildLogic.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Unity.Linker.Api.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/Unity.Options.dll"}, {"File": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline/UnityEditor.iOS.Extensions.Xcode.dll"}, {"File": "Library/Bee/1300b0aESkipCompile-inputdata.json"}], "StatSignatures": [{"File": "Assets/csc.rsp"}, {"File": "Assets/mcs.rsp"}, {"File": "Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"}, {"File": "Assets/Plugins/Demigiant/DOTween/DOTween.dll"}, {"File": "Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"}, {"File": "Library/Bee/1300b0aESkipCompile-inputdata.json"}, {"File": "Library/PackageCache/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.dll"}, {"File": "Library/PackageCache/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Mdb.dll"}, {"File": "Library/PackageCache/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Pdb.dll"}, {"File": "Library/PackageCache/com.unity.burst/Unity.Burst.CodeGen/Unity.Burst.Cecil.Rocks.dll"}, {"File": "Library/PackageCache/com.unity.burst/Unity.Burst.Unsafe.dll"}, {"File": "Library/PackageCache/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"}, {"File": "Library/PackageCache/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll"}, {"File": "Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.dll"}, {"File": "Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.Mdb.dll"}, {"File": "Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.Pdb.dll"}, {"File": "Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.Rocks.dll"}, {"File": "Library/PackageCache/com.unity.nuget.newtonsoft-json/Runtime/Newtonsoft.Json.dll"}, {"File": "Library/PackageCache/com.unity.testtools.codecoverage/lib/ReportGenerator/ReportGeneratorMerged.dll"}, {"File": "Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll"}, {"File": "Library/ScriptAssemblies/Assembly-CSharp.dll"}, {"File": "Library/ScriptAssemblies/Unity.Burst.dll"}, {"File": "Library/ScriptAssemblies/Unity.Cinemachine.dll"}, {"File": "Library/ScriptAssemblies/Unity.Collections.dll"}, {"File": "Library/ScriptAssemblies/Unity.InputSystem.dll"}, {"File": "Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}, {"File": "Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}, {"File": "Library/ScriptAssemblies/Unity.Mathematics.dll"}, {"File": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}, {"File": "Library/ScriptAssemblies/Unity.PerformanceTesting.dll"}, {"File": "Library/ScriptAssemblies/Unity.Postprocessing.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll"}, {"File": "Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}, {"File": "Library/ScriptAssemblies/Unity.Splines.dll"}, {"File": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}, {"File": "Library/ScriptAssemblies/Unity.Timeline.dll"}, {"File": "Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}, {"File": "Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}, {"File": "Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}, {"File": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}, {"File": "Library/ScriptAssemblies/UnityEngine.UI.dll"}], "GlobSignatures": [{"Path": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}], "ContentDigestExtensions": [".rsp", ".dll", ".exe", ".pdb", ".json"], "StructuredLogFileName": "Library/Bee/tundra.log.json", "StateFileName": "Library/Bee/TundraBuildState.state", "StateFileNameTmp": "Library/Bee/TundraBuildState.state.tmp", "StateFileNameMapped": "Library/Bee/TundraBuildState.state.map", "ScanCacheFileName": "Library/Bee/tundra.scancache", "ScanCacheFileNameTmp": "Library/Bee/tundra.scancache.tmp", "DigestCacheFileName": "Library/Bee/tundra.digestcache", "DigestCacheFileNameTmp": "Library/Bee/tundra.digestcache.tmp", "CachedNodeOutputDirectoryName": "Library/Bee/CachedNodeOutput", "EmitDataForBeeWhy": 0, "NamedNodes": {"all_tundra_nodes": 0, "ScriptAssemblies": 1, "ScriptAssembliesAndTypeDB": 3}, "DefaultNodes": [0], "SharedResources": [], "Scanners": [], "Identifier": "Library/Bee/1300b0aESkipCompile.dag.json", "PayloadsFile": "D:/My Project/Driving Simulator Game Z TEC/Library/Bee/1300b0aESkipCompile.dag.payloads", "RelativePathToRoot": "../.."}